import type { Metadata } from 'next';
import './globals.css';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from '@/components/ui/sonner';
import { ThemeProvider } from '@/components/theme-provider';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { APP_NAME } from '@/lib/constants';
import { AuthProvider } from '@/contexts/AuthContext';

export const metadata: Metadata = {
  title: `${APP_NAME} - تنمية الطفولة المبكرة`,
  description: `${APP_NAME}: التقييم والتنمية للطفولة المبكرة بناءً على برنامج بورتيج.`,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider 
          attribute="class" 
          defaultTheme="system" 
          enableSystem 
          disableTransitionOnChange
        >
          <AuthProvider>
            <SidebarProvider>
              <AuthenticatedLayout>{children}</AuthenticatedLayout>
              <Toaster />
              <SonnerToaster position="top-left" dir="rtl" />
            </SidebarProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
