"use client";

import * as React from "react";
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Bell, 
  Search, 
  Users, 
  FileText, 
  Plus,
  Menu,
  LogOut,
  User,
  Settings,
  HelpCircle
} from 'lucide-react';
import { APP_NAME } from '@/lib/constants';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { useKeyboardShortcuts } from "@/hooks/use-keyboard-shortcuts"
import { useChildren, useAssessments } from "@/hooks/use-storage"
import { calculateAge, formatDate } from "@/lib/utils"
import { differenceInDays, parseISO, addMonths, isPast, isValid } from "date-fns"
import { useSession, signOut } from "next-auth/react"
import { toast } from "sonner"

export default function AppHeader() {
  const [searchOpen, setSearchOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const [notificationsOpen, setNotificationsOpen] = React.useState(false)
  const { data: session } = useSession()
  const router = useRouter()

  const { children, isLoading: childrenLoading } = useChildren()
  const { assessments } = useAssessments()

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    'ctrl+k': () => setSearchOpen(true),
    'cmd+k': () => setSearchOpen(true),
  })

  // Generate notifications for overdue assessments and upcoming due dates
  const notifications = React.useMemo(() => {
    if (childrenLoading) return []
    
    const notifs: Array<{
      id: string
      title: string
      description: string
      href: string
      type: 'overdue' | 'due-soon' | 'initial' | 'service-complete'
      urgent: boolean
    }> = []
    
    const today = new Date()
    
    children.forEach(child => {
      // Skip if child doesn't have a valid dateOfBirth
      if (!child.dateOfBirth) {
        return
      }

      try {
        const age = calculateAge(child.dateOfBirth)
        const childAssessments = assessments.filter(a => a.childId === child.id)
        
        // Check for overdue assessments
        if (childAssessments.length > 0) {
          const lastAssessment = childAssessments.sort((a, b) => 
            new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime()
          )[0]
          
          const daysSinceLastAssessment = differenceInDays(today, parseISO(lastAssessment.assessmentDate))
          
          if (daysSinceLastAssessment > 90) { // Overdue if more than 3 months
            notifs.push({
              id: `overdue-${child.id}`,
              title: `تقييم متأخر: ${child.name}`,
              description: `آخر تقييم كان منذ ${daysSinceLastAssessment} يومًا`,
              href: `/children/${child.id}`,
              type: 'overdue',
              urgent: daysSinceLastAssessment > 120
            })
          } else if (daysSinceLastAssessment > 75) { // Due soon if more than 2.5 months
            notifs.push({
              id: `due-soon-${child.id}`,
              title: `تقييم مطلوب قريبًا: ${child.name}`,
              description: `آخر تقييم كان منذ ${daysSinceLastAssessment} يومًا`,
              href: `/children/${child.id}`,
              type: 'due-soon',
              urgent: false
            })
          }
        } else {
          // No assessments yet - initial assessment needed
          notifs.push({
            id: `initial-${child.id}`,
            title: `تقييم أولي مطلوب: ${child.name}`,
            description: `لم يتم إجراء أي تقييم بعد`,
            href: `/children/${child.id}`,
            type: 'initial',
            urgent: true
          })
        }
        
        // Check for completed service programs
        if (age.months >= 72) { // 6 years old
          const hasRecentAssessment = childAssessments.some(a => {
            const assessmentDate = parseISO(a.assessmentDate)
            return differenceInDays(today, assessmentDate) <= 30
          })
          
          if (hasRecentAssessment) {
            notifs.push({
              id: `service-complete-${child.id}`,
              title: `برنامج الخدمة مكتمل: ${child.name}`,
              description: `الطفل أصبح في سن المدرسة (${age.years} سنوات)`,
              href: `/children/${child.id}`,
              type: 'service-complete',
              urgent: false
            })
          }
        }
      } catch (error) {
        console.warn(`Error processing child ${child.id}:`, error)
        // Skip this child if there's any error with date processing
      }
    })
    
    return notifs.sort((a, b) => {
      if (a.urgent && !b.urgent) return -1
      if (!a.urgent && b.urgent) return 1
      return 0
    })
  }, [children, assessments, childrenLoading])

  // Search items
  const searchItems = React.useMemo(() => {
    if (childrenLoading) return []
    
    const items: Array<{
      id: string
      label: string
      value: string
      group: string
      href: string
      icon: any
    }> = []
    
    // Add children to search
    children.forEach(child => {
      items.push({
        id: `child-${child.id}`,
        label: child.name,
        value: child.name.toLowerCase(),
        group: "الأطفال",
        href: `/children/${child.id}`,
        icon: Users
      })
    })

    // Add assessments to search
    assessments.forEach(assessment => {
      const child = children.find(c => c.id === assessment.childId)
      if (child) {
        items.push({
          id: `assessment-${assessment.id}`,
          label: `تقييم ${child.name} - ${formatDate(assessment.assessmentDate)}`,
          value: `تقييم ${child.name}`.toLowerCase(),
          group: "التقييمات",
          href: `/children/${assessment.childId}/assessment/${assessment.id}`,
          icon: FileText
        })
      }
    })

    return items
  }, [children, assessments, childrenLoading])

  const filteredItems = searchItems.filter(item =>
    item.value.includes(searchValue.toLowerCase())
  )

  const urgentNotifications = notifications.filter(n => n.urgent).length
  const totalNotifications = notifications.length

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'overdue': return '🚨'
      case 'due-soon': return '⏰'
      case 'initial': return '📋'
      case 'service-complete': return '🎓'
      default: return '📢'
    }
  }

  const getNotificationColor = (type: string, urgent: boolean) => {
    if (urgent) return 'destructive'
    switch (type) {
      case 'due-soon': return 'default'
      case 'initial': return 'secondary'
      case 'service-complete': return 'outline'
      default: return 'default'
    }
  }

  const handleLogout = async () => {
    try {
      await signOut({ redirect: false })
      toast.success("تم تسجيل الخروج بنجاح")
      router.push("/login")
      router.refresh()
    } catch (error) {
      console.error('Logout error:', error)
      toast.error("حدث خطأ أثناء تسجيل الخروج")
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center px-2 md:px-4 lg:px-6 gap-2 md:gap-4 overflow-hidden">
        {/* Mobile sidebar trigger */}
        <div className="md:hidden flex-shrink-0">
          <SidebarTrigger className="h-8 w-8" />
        </div>
        
        {/* Logo and brand */}
        <div className="flex items-center gap-2 md:gap-4 lg:gap-6 flex-shrink-0">
          {/* Removed logo/brand section to avoid duplication with sidebar */}
        </div>

        {/* Search */}
        <div className="header-search flex-1 max-w-sm mx-2 md:max-w-md md:mx-4 min-w-0">
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="relative w-full justify-start text-sm text-muted-foreground h-9 px-2 md:px-3 min-w-0"
              >
                <Search className="h-4 w-4 shrink-0 ml-1 md:ml-2" />
                {/* Show full text on md+ screens, just icon on mobile */}
                <span className="hidden md:flex flex-1 text-right truncate">
                  البحث في الأطفال والتقييمات...
                </span>
                <span className="md:hidden flex-1 text-right text-xs truncate">
                  بحث...
                </span>
                <kbd className="pointer-events-none hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100 lg:flex">
                  <span className="text-xs">Ctrl</span>K
                </kbd>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[90vw] md:w-96 p-0" align="end">
              <Command className="rounded-lg border-0 shadow-md">
                <CommandInput 
                  placeholder="البحث في الأطفال والتقييمات..."
                  value={searchValue}
                  onValueChange={setSearchValue}
                  className="h-9"
                />
                <CommandList className="max-h-64">
                  <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                    لا توجد نتائج للبحث
                  </CommandEmpty>
                  {Object.entries(
                    filteredItems.reduce((groups, item) => {
                      if (!groups[item.group]) groups[item.group] = []
                      groups[item.group].push(item)
                      return groups
                    }, {} as Record<string, typeof filteredItems>)
                  ).map(([group, items]) => (
                    <CommandGroup key={group} heading={group}>
                      {items.slice(0, 5).map((item) => (
                        <CommandItem
                          key={item.id}
                          value={item.value}
                          onSelect={() => {
                            setSearchOpen(false)
                            setSearchValue("")
                          }}
                          className="flex items-center gap-3 px-3 py-2"
                          asChild
                        >
                          <Link href={item.href}>
                            <item.icon className="h-4 w-4 shrink-0" />
                            <span className="flex-1 text-right truncate">
                              {item.label}
                            </span>
                          </Link>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ))}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        <div className="header-actions flex items-center gap-1 md:gap-2 flex-shrink-0">
          {/* Notifications */}
          <Popover open={notificationsOpen} onOpenChange={setNotificationsOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 relative">
                <Bell className="h-4 w-4" />
                {totalNotifications > 0 && (
                  <Badge 
                    variant={urgentNotifications > 0 ? "destructive" : "secondary"}
                    className="absolute -top-1 -right-1 h-4 w-4 md:h-5 md:w-5 rounded-full p-0 text-[10px] md:text-xs flex items-center justify-center"
                  >
                    {totalNotifications > 9 ? '9+' : totalNotifications}
                  </Badge>
                )}
                <span className="sr-only">الإشعارات</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[90vw] md:w-80" align="end">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">الإشعارات</CardTitle>
                    <Badge variant="outline" className="text-xs">
                      {totalNotifications} جديد
                    </Badge>
                  </div>
                  {urgentNotifications > 0 && (
                    <CardDescription className="text-destructive">
                      {urgentNotifications} إشعار عاجل يتطلب انتباهًا فوريًا
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent className="p-0">
                  {notifications.length > 0 ? (
                    <div className="space-y-1 max-h-64 overflow-y-auto">
                      {notifications.slice(0, 5).map((notification, index) => (
                        <div key={notification.id}>
                          <Link
                            href={notification.href}
                            className="block p-3 hover:bg-muted/50 transition-colors"
                            onClick={() => setNotificationsOpen(false)}
                          >
                            <div className="flex items-start gap-3">
                              <span className="text-lg shrink-0 mt-0.5">
                                {getNotificationIcon(notification.type)}
                              </span>
                              <div className="flex-1 space-y-1">
                                <div className="flex items-center gap-2">
                                  <p className="text-sm font-medium text-right flex-1">
                                    {notification.title}
                                  </p>
                                  <Badge
                                    variant={getNotificationColor(notification.type, notification.urgent)}
                                    className="text-xs shrink-0"
                                  >
                                    {notification.urgent ? 'عاجل' : 'جديد'}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground text-right">
                                  {notification.description}
                                </p>
                              </div>
                            </div>
                          </Link>
                          {index < notifications.slice(0, 5).length - 1 && (
                            <Separator className="mx-3" />
                          )}
                        </div>
                      ))}
                      {notifications.length > 5 && (
                        <div className="p-3 text-center">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href="/notifications">
                              عرض جميع الإشعارات ({notifications.length})
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">
                      <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">لا توجد إشعارات جديدة</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </PopoverContent>
          </Popover>

          {/* Theme Toggle */}
          <div className="hidden sm:block">
            <ThemeToggle />
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 rounded-full p-0">
                <Avatar className="h-7 w-7 md:h-8 md:w-8">
                  <AvatarImage src="/placeholder-avatar.jpg" alt="المستخدم" />
                  <AvatarFallback className="text-[10px] md:text-xs font-medium bg-primary text-primary-foreground">
                    {session?.user?.name?.slice(0, 2)?.toUpperCase() || 'أح'}
                  </AvatarFallback>
                </Avatar>
                <span className="sr-only">قائمة المستخدم</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-48 md:w-56" align="end">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1 text-right">
                  <p className="text-sm font-medium leading-none">{session?.user?.name || 'المستخدم'}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {session?.user?.email || '<EMAIL>'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  الملف الشخصي
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  الإعدادات
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/help" className="flex items-center gap-2">
                  <HelpCircle className="h-4 w-4" />
                  المساعدة
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive flex items-center gap-2 cursor-pointer"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                تسجيل الخروج
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
