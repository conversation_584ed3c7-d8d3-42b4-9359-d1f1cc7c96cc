'use client';

import { useState } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"form">) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login, register } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error("يرجى إدخال البريد الإلكتروني وكلمة المرور");
      return;
    }

    if (isRegistering && !name) {
      toast.error("يرجى إدخال الاسم");
      return;
    }

    setIsLoading(true);
    
    try {
      let success;
      if (isRegistering) {
        success = await register({ email, password, name });
      } else {
        success = await login(email, password);
      }
      
      if (success) {
        toast.success(isRegistering ? "تم التسجيل بنجاح" : "تم تسجيل الدخول بنجاح");
        router.push("/");
      } else {
        toast.error(isRegistering ? "حدث خطأ أثناء التسجيل" : "خطأ في البريد الإلكتروني أو كلمة المرور");
      }
    } catch (error) {
      toast.error(isRegistering ? "حدث خطأ أثناء التسجيل" : "حدث خطأ أثناء تسجيل الدخول");
    }
    
    setIsLoading(false);
  };

  return (
    <form className={cn("flex flex-col gap-6", className)} onSubmit={handleSubmit} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold arabic-text">{isRegistering ? "إنشاء حساب جديد" : "تسجيل الدخول"}</h1>
        <p className="text-balance text-sm text-muted-foreground arabic-text">
          {isRegistering ? "أدخل بياناتك أدناه لإنشاء حساب جديد" : "أدخل بريدك الإلكتروني أدناه لتسجيل الدخول إلى حسابك"}
        </p>
      </div>
      <div className="grid gap-6">
        {isRegistering && (
          <div className="grid gap-2">
            <Label htmlFor="name" className="arabic-text">الاسم</Label>
            <Input 
              id="name" 
              type="text" 
              placeholder="الاسم" 
              required 
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
            />
          </div>
        )}
        <div className="grid gap-2">
          <Label htmlFor="email" className="arabic-text">البريد الإلكتروني</Label>
          <Input 
            id="email" 
            type="email" 
            placeholder="<EMAIL>" 
            required 
            dir="ltr"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <div className="grid gap-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="arabic-text">كلمة المرور</Label>
            {!isRegistering && (
              <a
                href="#"
                className="text-sm underline-offset-4 hover:underline arabic-text"
              >
                هل نسيت كلمة المرور؟
              </a>
            )}
          </div>
          <Input 
            id="password" 
            type="password" 
            required 
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <Button type="submit" className="w-full arabic-text" disabled={isLoading}>
          {isLoading ? (isRegistering ? "جاري إنشاء الحساب..." : "جاري تسجيل الدخول...") : (isRegistering ? "إنشاء حساب" : "تسجيل الدخول")}
        </Button>
        <Button variant="outline" type="button" className="w-full arabic-text" onClick={() => setIsRegistering(!isRegistering)} disabled={isLoading}>
          {isRegistering ? "العودة لتسجيل الدخول" : "إنشاء حساب جديد"}
        </Button>
      </div>
    </form>
  )
}
