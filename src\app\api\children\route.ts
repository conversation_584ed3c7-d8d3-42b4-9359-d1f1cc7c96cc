import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const children = await prisma.child.findMany();
  return NextResponse.json(children);
}

export async function POST(request: Request) {
  const data = await request.json();
  const child = await prisma.child.create({ data });
  return NextResponse.json(child, { status: 201 });
}
