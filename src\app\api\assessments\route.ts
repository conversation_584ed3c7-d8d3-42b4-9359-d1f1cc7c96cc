import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const assessments = await prisma.assessment.findMany();
  return NextResponse.json(assessments);
}

export async function POST(request: Request) {
  const data = await request.json();
  const assessment = await prisma.assessment.create({ data });
  return NextResponse.json(assessment, { status: 201 });
}
