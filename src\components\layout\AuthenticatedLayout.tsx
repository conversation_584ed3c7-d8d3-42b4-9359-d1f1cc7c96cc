'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from './MainLayout';

const publicRoutes = ['/login'];

export default function AuthenticatedLayout({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't redirect if still loading or on public routes
    if (isLoading || publicRoutes.includes(pathname)) {
      return;
    }

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground arabic-text">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  // For public routes, render children without MainLayout
  if (publicRoutes.includes(pathname)) {
    return <>{children}</>;
  }

  // For authenticated routes, render children with MainLayout if authenticated
  if (isAuthenticated) {
    return <MainLayout>{children}</MainLayout>;
  }

  // Return null while redirecting
  return null;
} 