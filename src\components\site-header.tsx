"use client"

import * as React from "react"
import { Bell, Search as SearchIcon, Users, FileText, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useChildren, useAssessments } from "@/hooks/use-storage"
import { calculateAge, formatDate } from "@/lib/utils"
import { differenceInDays, parseISO, addMonths, isPast, isValid } from "date-fns"
import Link from "next/link"

export function SiteHeader() {
  const [searchOpen, setSearchOpen] = React.useState(false)
  const [notificationsOpen, setNotificationsOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  
  const { children, loading: childrenLoading } = useChildren()
  const { assessments, loading: assessmentsLoading } = useAssessments()

  // Helper to get the latest assessment for a child
  const getLatestAssessmentForChild = (childId: string) => {
    return assessments
      .filter(a => a.childId === childId)
      .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0]
  }

  // Calculate notifications
  const notifications = React.useMemo(() => {
    if (childrenLoading || assessmentsLoading) return []
    
    const alerts: Array<{
      id: string
      type: 'overdue' | 'due-soon' | 'initial' | 'service-complete'
      title: string
      description: string
      href: string
      urgent: boolean
    }> = []

    const today = new Date()

    children.forEach(child => {
      const age = calculateAge(child.birthDate, today.toISOString())
      
      // Service completion alerts
      if (age.years >= 6) {
        alerts.push({
          id: `service-${child.id}`,
          type: 'service-complete',
          title: 'اكتمال الخدمة',
          description: `${child.name} - بلغ 6 سنوات أو أكثر`,
          href: `/children/${child.id}`,
          urgent: false
        })
        return // Skip other checks for completed services
      }

      const latestAssessment = getLatestAssessmentForChild(child.id)
      
      if (latestAssessment) {
        try {
          const lastAssessmentDate = parseISO(latestAssessment.assessmentDate)
          if (isValid(lastAssessmentDate)) {
            const reassessmentDueDate = addMonths(lastAssessmentDate, 4)
            
            if (isPast(reassessmentDueDate)) {
              alerts.push({
                id: `overdue-${child.id}`,
                type: 'overdue',
                title: 'إعادة تقييم مطلوبة فورًا',
                description: `${child.name} - آخر تقييم منذ أكثر من 4 أشهر`,
                href: `/children/${child.id}/assessment`,
                urgent: true
              })
            } else {
              const daysUntilReassessment = differenceInDays(reassessmentDueDate, today)
              if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {
                alerts.push({
                  id: `due-soon-${child.id}`,
                  type: 'due-soon',
                  title: 'إعادة تقييم قريبة',
                  description: `${child.name} - الموعد المتوقع: ${formatDate(reassessmentDueDate.toISOString())}`,
                  href: `/children/${child.id}/assessment`,
                  urgent: false
                })
              }
            }
          }
        } catch (error) {
          // Handle date parsing errors silently
        }
      } else {
        alerts.push({
          id: `initial-${child.id}`,
          type: 'initial',
          title: 'تقييم أولي مطلوب',
          description: `${child.name} - لا يوجد تقييم مسجل`,
          href: `/children/${child.id}/assessment/new`,
          urgent: false
        })
      }
    })

    return alerts
  }, [children, assessments, childrenLoading, assessmentsLoading])

  // Search items
  const searchItems = React.useMemo(() => {
    if (childrenLoading) return []
    
    const items: Array<{
      id: string
      label: string
      value: string
      group: string
      href: string
      icon: any
    }> = []
    
    // Add children to search
    children.forEach(child => {
      items.push({
        id: `child-${child.id}`,
        label: child.name,
        value: child.name.toLowerCase(),
        group: "الأطفال",
        href: `/children/${child.id}`,
        icon: Users
      })
    })

    // Add assessments to search
    assessments.forEach(assessment => {
      const child = children.find(c => c.id === assessment.childId)
      if (child) {
        items.push({
          id: `assessment-${assessment.id}`,
          label: `تقييم ${child.name} - ${formatDate(assessment.assessmentDate)}`,
          value: `تقييم ${child.name}`.toLowerCase(),
          group: "التقييمات",
          href: `/children/${assessment.childId}/assessment/${assessment.id}`,
          icon: FileText
        })
      }
    })

    return items
  }, [children, assessments, childrenLoading])

  const filteredItems = searchItems.filter(item =>
    item.value.includes(searchValue.toLowerCase())
  )

  const urgentNotifications = notifications.filter(n => n.urgent).length

  return (
    <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">لوحة التحكم</h1>
        
        {/* Search */}
        <div className="flex-1 max-w-md mx-4">
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={searchOpen}
                className="w-full justify-start text-muted-foreground"
              >
                <SearchIcon className="mr-2 h-4 w-4" />
                البحث في الأطفال والتقييمات...
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <Command>
                <CommandInput 
                  placeholder="البحث..." 
                  value={searchValue}
                  onValueChange={setSearchValue}
                />
                <CommandList>
                  <CommandEmpty>لا توجد نتائج.</CommandEmpty>
                  {filteredItems.length > 0 && (
                    <>
                      {/* Group by category */}
                      {["الأطفال", "التقييمات"].map(group => {
                        const groupItems = filteredItems.filter(item => item.group === group)
                        if (groupItems.length === 0) return null
                        
                        return (
                          <CommandGroup key={group} heading={group}>
                            {groupItems.map((item) => (
                              <CommandItem
                                key={item.id}
                                onSelect={() => {
                                  setSearchOpen(false)
                                  window.location.href = item.href
                                }}
                              >
                                <item.icon className="mr-2 h-4 w-4" />
                                {item.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )
                      })}
                    </>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        {/* Notifications */}
        <Popover open={notificationsOpen} onOpenChange={setNotificationsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              {urgentNotifications > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs"
                >
                  {urgentNotifications}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">الإشعارات</CardTitle>
                  {notifications.length > 0 && (
                    <Badge variant="secondary">{notifications.length}</Badge>
                  )}
                </div>
                <CardDescription>
                  تنبيهات مهمة تحتاج لمتابعة
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                {notifications.length === 0 ? (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    لا توجد إشعارات جديدة
                  </div>
                ) : (
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <Link
                        key={notification.id}
                        href={notification.href}
                        className="block border-b last:border-b-0 p-3 hover:bg-muted/50 transition-colors"
                        onClick={() => setNotificationsOpen(false)}
                      >
                        <div className="flex items-start gap-2">
                          <div className={`mt-1 h-2 w-2 rounded-full flex-shrink-0 ${
                            notification.urgent ? 'bg-red-500' : 'bg-blue-500'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium leading-none">
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {notification.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>
      </div>
    </header>
  )
}
