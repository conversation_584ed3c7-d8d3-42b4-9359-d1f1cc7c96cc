import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  const { email, password, name, role, specialization } = await request.json();

  if (!email || !password || !name) {
    return NextResponse.json({ error: 'Email, password, and name are required' }, { status: 400 });
  }

  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  if (existingUser) {
    return NextResponse.json({ error: 'User already exists' }, { status: 409 });
  }

  // In a real app, you should hash passwords securely
  // For this demo, we'll store the password as-is
  // This is NOT secure and should be replaced
  const user = await prisma.user.create({
    data: {
      email,
      // @ts-ignore
      password,
      name,
      role: role || 'viewer',
      specialization,
    },
  });

  // @ts-ignore
  const { password: _, ...userWithoutPassword } = user;

  return NextResponse.json(userWithoutPassword, { status: 201 });
}
