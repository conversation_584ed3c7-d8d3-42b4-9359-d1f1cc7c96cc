import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { differenceInYears, differenceInMonths, differenceInDays, addYears, addMonths, parseISO, isValid, format } from 'date-fns';
import { arSA } from 'date-fns/locale'; // Import Arabic locale
import type { CalculatedAge } from './types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function calculateAge(birthDateString: string, assessmentDateString?: string): CalculatedAge {
  const birthDate = parseISO(birthDateString);
  const assessmentDate = assessmentDateString ? parseISO(assessmentDateString) : new Date();

  if (!isValid(birthDate) || !isValid(assessmentDate)) {
    // console.error("Invalid date provided for age calculation", { birthDateString, assessmentDateString });
    return { years: 0, months: 0, days: 0 };
  }

  if (birthDate > assessmentDate) {
    return { years: 0, months: 0, days: 0 };
  }

  let tempAgeDate = new Date(birthDate);

  const years = differenceInYears(assessmentDate, tempAgeDate);
  tempAgeDate = addYears(tempAgeDate, years);

  const months = differenceInMonths(assessmentDate, tempAgeDate);
  tempAgeDate = addMonths(tempAgeDate, months);

  const days = differenceInDays(assessmentDate, tempAgeDate);

  return { years, months, days };
}

export function formatDate(dateString: string | Date, dateFormat: string = 'PPP'): string {
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
    if (!isValid(date)) return "تاريخ غير صالح";
    return format(date, dateFormat, { locale: arSA }); // Use Arabic locale
  } catch (error) {
    return "تاريخ غير صالح";
  }
}

// Generate a unique child ID number
export function generateChildIdNumber(): string {
  const currentYear = new Date().getFullYear();
  const randomNumber = Math.floor(Math.random() * 999) + 1;
  return `CH-${currentYear}-${randomNumber.toString().padStart(3, '0')}`;
}

// Generate unique IDs for various entities
export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;
}
