import type { Metadata } from 'next';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from '@/components/ui/sonner';
import { APP_NAME } from '@/lib/constants';

export const metadata: Metadata = {
  title: `تسجيل الدخول - ${APP_NAME}`,
  description: `تسجيل الدخول إلى ${APP_NAME}`,
};

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider 
      attribute="class" 
      defaultTheme="system" 
      enableSystem 
      disableTransitionOnChange
    >
      {children}
      <Toaster />
      <SonnerToaster position="top-left" dir="rtl" />
    </ThemeProvider>
  );
} 