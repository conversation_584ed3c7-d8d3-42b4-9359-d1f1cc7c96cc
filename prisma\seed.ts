import { PrismaClient } from '@prisma/client';
import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, MOCK_USERS_DATA } from '../src/lib/constants';

const prisma = new PrismaClient();

async function main() {
  console.log('Start seeding ...');

  for (const userData of MOCK_USERS_DATA) {
    const { avatarUrl, ...rest } = userData;
    await prisma.user.create({
      data: {
        ...rest,
        password: 'password123',
        image: avatarUrl,
      } as any,
    });
  }

  for (const childData of MOCK_CHILDREN_DATA) {
    const { assessmentIds, sessionNotes, caseStudy, ...rest } = childData;
    await prisma.child.create({
      data: {
        ...rest,
        birthDate: new Date(childData.birthDate),
        enrollmentDate: new Date(childData.enrollmentDate),
      },
    });
  }

  for (const assessmentData of MOCK_ASSESSMENTS_DATA) {
    await prisma.assessment.create({
      data: {
        ...assessmentData,
        assessmentDate: new Date(assessmentData.assessmentDate),
        assessedSkills: assessmentData.assessedSkills as any,
        baselines: assessmentData.baselines as any,
        ceilings: assessmentData.ceilings as any,
      },
    });
  }

  console.log('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
