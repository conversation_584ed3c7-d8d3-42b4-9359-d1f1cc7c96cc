// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Child {
  id                  String                @id @default(cuid())
  childIdNumber       String                @unique
  name                String
  birthDate           DateTime
  enrollmentDate      DateTime
  specialistName      String
  avatarUrl           String?
  gender              String?
  caseStudyNotes      String?
  isDeleted           Boolean?              @default(false)
  assessments         Assessment[]
  learningPlans       LearningPlan[]
  comprehensiveReports ComprehensiveReport[]
  sessionNotes        SessionNote[]
  planNotes           PlanNote[]
  caseStudy           CaseStudyData?
}

model Assessment {
  id              String         @id @default(cuid())
  childId         String
  assessmentDate  DateTime
  assessedSkills  Json
  baselines       Json?
  ceilings        Json?
  child           Child          @relation(fields: [childId], references: [id])
  learningPlans   LearningPlan[]
  comprehensiveReports ComprehensiveReport[]
}

model LearningPlan {
  id            String     @id @default(cuid())
  childId       String
  assessmentId  String
  generatedDate DateTime
  planDetails   String     @db.Text
  suggestedDailyGoals String? @db.Text
  child         Child      @relation(fields: [childId], references: [id])
  assessment    Assessment @relation(fields: [assessmentId], references: [id])
}

model ComprehensiveReport {
  id                      String     @id @default(cuid())
  childId                 String
  assessmentId            String
  generatedDate           DateTime
  childName               String
  assessmentDate          DateTime
  childAgeInMonths        Int
  additionalFocus         String?
  executiveSummary        String     @db.Text
  strengths               String     @db.Text
  areasForDevelopment     String     @db.Text
  dataAnalysisHighlights  String     @db.Text
  actionableRecommendations String  @db.Text
  child                   Child      @relation(fields: [childId], references: [id])
  assessment              Assessment @relation(fields: [assessmentId], references: [id])
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?  @db.Text
  access_token       String?  @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?  @db.Text
  session_state      String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("viewer")
  specialization String?
  accounts      Account[]
  sessions      Session[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model SessionNote {
  id            String   @id @default(cuid())
  date          DateTime
  goalDiscussed String
  attendees     String
  notes         String   @db.Text
  nextSteps     String?
  childId       String
  child         Child    @relation(fields: [childId], references: [id])
}

model PlanNote {
  id            String   @id @default(cuid())
  childId       String
  skillId       String?
  planType      String
  skillBehavior String?
  notes         String   @db.Text
  isGlobal      Boolean
  createdDate   DateTime
  lastModified  DateTime
  child         Child    @relation(fields: [childId], references: [id])
}

model CaseStudyData {
  id                      String   @id @default(cuid())
  childId                 String   @unique
  child                   Child    @relation(fields: [childId], references: [id])
  basicInfo               Json
  pregnancyAndBirthInfo   Json
  reinforcerResponseInfo  Json
}
