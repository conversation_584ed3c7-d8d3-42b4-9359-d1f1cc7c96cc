import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  const { email, password } = await request.json();

  if (!email || !password) {
    return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
  }

  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (!user) {
    return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
  }

  // In a real app, you should hash and compare passwords securely
  // For this demo, we'll do a simple comparison
  // This is NOT secure and should be replaced
  // @ts-ignore
  if (user.password !== password) {
    return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
  }
  // @ts-ignore
  const { password: _, ...userWithoutPassword } = user;

  // Simulate setting user in session
  (global as any).user = userWithoutPassword;

  return NextResponse.json(userWithoutPassword);
}
